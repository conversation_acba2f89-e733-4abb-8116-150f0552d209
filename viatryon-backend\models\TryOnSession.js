const mongoose = require('mongoose');

const tryOnSessionSchema = new mongoose.Schema({
  // Session identification
  sessionId: {
    type: String,
    required: true,
    unique: true
  },
  
  // Client information
  clientId: {
    type: mongoose.Schema.Types.Mixed,
    ref: 'User',
    required: true
  },
  
  // Product information
  productId: {
    type: String,
    required: true
  },
  productName: {
    type: String,
    required: true
  },
  productCategory: {
    type: String,
    enum: ['watches', 'bracelets'],
    required: true
  },
  
  // Session details
  startTime: {
    type: Date,
    required: true,
    default: Date.now
  },
  endTime: {
    type: Date
  },
  duration: {
    type: Number // in seconds
  },
  
  // User behavior metrics
  behaviorMetrics: {
    timeToFirstInteraction: Number, // seconds
    cameraInitSuccess: Boolean,
    handDetectionSuccess: Boolean,
    backgroundRemovalSuccess: Boolean,
    productSwitches: {
      type: Number,
      default: 0
    },
    productViewTimes: [{
      productId: String,
      duration: Number // seconds
    }],
    // Enhanced Tangiblee-like metrics
    engagementScore: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    scrollDepth: {
      type: Number,
      default: 0 // percentage of page scrolled
    },
    mouseMovements: {
      type: Number,
      default: 0 // total mouse movement distance
    },
    hoverEvents: [{
      element: String,
      duration: Number, // milliseconds
      timestamp: Date
    }],
    gestureEvents: [{
      type: {
        type: String,
        enum: ['pinch', 'zoom', 'rotate', 'swipe', 'tap', 'double_tap']
      },
      intensity: Number,
      timestamp: Date
    }],
    featureUsage: {
      cameraToggle: { type: Number, default: 0 },
      productRotation: { type: Number, default: 0 },
      sizeAdjustment: { type: Number, default: 0 },
      colorChange: { type: Number, default: 0 },
      screenshot: { type: Number, default: 0 },
      share: { type: Number, default: 0 },
      zoom: { type: Number, default: 0 }
    },
    exitIntent: {
      detected: { type: Boolean, default: false },
      timestamp: Date,
      beforeConversion: { type: Boolean, default: false }
    },
    attentionMetrics: {
      focusTime: { type: Number, default: 0 }, // milliseconds page was in focus
      blurEvents: { type: Number, default: 0 }, // times user left the page
      returnEvents: { type: Number, default: 0 }, // times user returned to page
      idleTime: { type: Number, default: 0 } // milliseconds of inactivity
    }
  },
  
  // Technical performance metrics
  performanceMetrics: {
    pageLoadTime: Number, // milliseconds
    apiResponseTimes: [{
      endpoint: String,
      duration: Number, // milliseconds
      timestamp: Date
    }],
    errors: [{
      type: String,
      message: String,
      timestamp: Date,
      stack: String,
      userAgent: String
    }],
    // Enhanced performance tracking
    frameRate: {
      average: Number,
      min: Number,
      max: Number,
      drops: Number // number of frame drops
    },
    memoryUsage: {
      used: Number, // bytes
      total: Number, // bytes
      timestamp: Date
    },
    networkMetrics: {
      connectionType: String, // '4g', 'wifi', etc.
      downlink: Number, // Mbps
      rtt: Number, // milliseconds
      effectiveType: String // 'slow-2g', '2g', '3g', '4g'
    },
    renderMetrics: {
      firstContentfulPaint: Number,
      largestContentfulPaint: Number,
      firstInputDelay: Number,
      cumulativeLayoutShift: Number
    },
    resourceLoadTimes: [{
      resource: String,
      loadTime: Number,
      size: Number,
      timestamp: Date
    }]
  },
  
  // User interaction data
  interactions: [{
    type: {
      type: String,
      enum: [
        'rotation',
        'color_change',
        'size_adjustment',
        'share',
        'screenshot',
        'zoom',
        'camera_init',
        'hand_detection',
        'background_removal',
        'product_switch',
        'hover',
        'click',
        'scroll',
        'gesture',
        'focus',
        'blur',
        'resize',
        'orientation_change',
        'feature_toggle',
        'error_recovery',
        'help_accessed',
        'tutorial_step',
        'other'
      ]
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    data: mongoose.Schema.Types.Mixed,
    // Enhanced interaction tracking
    position: {
      x: Number,
      y: Number
    },
    element: String, // CSS selector or element identifier
    duration: Number, // for hover, focus events
    intensity: Number, // for gestures, pressure
    sequence: Number, // order in interaction sequence
    context: String // additional context about the interaction
  }],
  
  // Technical data
  device: {
    type: {
      type: String,
      enum: ['mobile', 'tablet', 'desktop']
    },
    os: String,
    browser: String,
    screenResolution: String,
    userAgent: String
  },
  
  // Location data
  location: {
    timezone: String,
    referrer: String,
    ip: String,
    userIP: String, // Alternative field name for IP
    country: String,
    region: String,
    city: String
  },
  
  // Outcome data
  outcome: {
    type: String,
    enum: ['abandoned', 'completed', 'shared', 'purchased'],
    default: 'abandoned'
  },
  
  // Conversion tracking
  converted: {
    type: Boolean,
    default: false
  },
  conversionValue: {
    type: Number
  },
  conversionTime: {
    type: Date
  },

  // Enhanced conversion and analytics tracking
  conversionFunnel: {
    viewedProduct: { type: Boolean, default: true },
    initiatedTryOn: { type: Boolean, default: false },
    completedTryOn: { type: Boolean, default: false },
    sharedResult: { type: Boolean, default: false },
    addedToCart: { type: Boolean, default: false },
    proceededToCheckout: { type: Boolean, default: false },
    completedPurchase: { type: Boolean, default: false }
  },

  // A/B testing and experimentation
  experiments: [{
    name: String,
    variant: String,
    timestamp: Date
  }],

  // Quality metrics
  qualityMetrics: {
    handDetectionAccuracy: Number, // 0-1 score
    backgroundRemovalQuality: Number, // 0-1 score
    productFitAccuracy: Number, // 0-1 score
    userSatisfactionScore: Number, // 1-5 rating if provided
    technicalIssues: [{
      type: String,
      severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical']
      },
      timestamp: Date,
      resolved: { type: Boolean, default: false }
    }]
  },

  // Business intelligence
  businessMetrics: {
    customerLifetimeValue: Number,
    acquisitionCost: Number,
    retentionProbability: Number,
    churnRisk: Number,
    segmentCategory: String,
    marketingSource: String,
    campaignId: String
  }
}, {
  timestamps: true
});

// Indexes for performance
tryOnSessionSchema.index({ clientId: 1, createdAt: -1 });
tryOnSessionSchema.index({ productId: 1, createdAt: -1 });
tryOnSessionSchema.index({ sessionId: 1 });
tryOnSessionSchema.index({ userId: 1, createdAt: -1 });
tryOnSessionSchema.index({ outcome: 1, createdAt: -1 });

// Pre-save middleware to calculate duration
tryOnSessionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  if (this.endTime && this.startTime) {
    this.duration = Math.floor((this.endTime - this.startTime) / 1000);
  }
  
  next();
});

// Static methods for analytics
tryOnSessionSchema.statics.getAnalytics = function(clientId, startDate, endDate) {
  const match = { clientId };
  if (startDate && endDate) {
    match.createdAt = { $gte: startDate, $lte: endDate };
  }
  
  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        totalDuration: { $sum: '$duration' },
        avgDuration: { $avg: '$duration' },
        conversions: { $sum: { $cond: ['$converted', 1, 0] } },
        totalInteractions: { $sum: { $size: '$interactions' } }
      }
    }
  ]);
};

tryOnSessionSchema.statics.getProductAnalytics = function(clientId, startDate, endDate) {
  const match = { clientId };
  if (startDate && endDate) {
    match.createdAt = { $gte: startDate, $lte: endDate };
  }

  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: '$productId',
        productName: { $first: '$productName' },
        category: { $first: '$productCategory' },
        sessions: { $sum: 1 },
        conversions: { $sum: { $cond: ['$converted', 1, 0] } },
        avgDuration: { $avg: '$duration' },
        totalRevenue: { $sum: '$conversionValue' }
      }
    },
    { $sort: { sessions: -1 } }
  ]);
};

tryOnSessionSchema.statics.getDeviceStats = function(clientId, startDate, endDate) {
  const match = {
    clientId,
    'device.type': { $exists: true }
  };
  if (startDate && endDate) {
    match.createdAt = { $gte: startDate, $lte: endDate };
  }

  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: '$device.type',
        sessions: { $sum: 1 },
        conversions: { $sum: { $cond: ['$converted', 1, 0] } },
        avgDuration: { $avg: '$duration' }
      }
    },
    {
      $addFields: {
        conversionRate: {
          $cond: [
            { $gt: ['$sessions', 0] },
            { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
            0
          ]
        }
      }
    },
    { $sort: { sessions: -1 } }
  ]);
};

module.exports = mongoose.model('TryOnSession', tryOnSessionSchema);
