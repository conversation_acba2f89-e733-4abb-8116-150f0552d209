import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import { Eye, Users, TrendingUp, Clock } from 'lucide-react';

const Overview = ({ timeRange }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [overviewData, setOverviewData] = useState(null);

  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        
        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        // Fetch overview data (enhanced endpoint might not exist yet)
        const overviewResponse = await fetch(`${apiUrl}/api/analytics/admin/overview?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!overviewResponse.ok) {
          throw new Error('Failed to fetch overview data');
        }

        const overviewData = await overviewResponse.json();

        // Try to fetch enhanced data, but don't fail if it doesn't exist
        let enhancedData = {};
        try {
          const enhancedResponse = await fetch(`${apiUrl}/api/analytics/admin/enhanced-overview?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          if (enhancedResponse.ok) {
            enhancedData = await enhancedResponse.json();
          }
        } catch (err) {
          console.warn('Enhanced analytics not available:', err);
        }

        // Combine the data with fallbacks for missing enhanced metrics
        const combinedData = {
          ...overviewData,
          ...enhancedData,

          // Basic metrics with proper fallbacks
          totalSessions: overviewData.totalSessions || 0,
          activeClients: overviewData.activeClients || overviewData.totalClients || 0,
          avgSessionDuration: overviewData.avgSessionDuration || 0,
          conversionRate: overviewData.conversionRate || 0,

          // Growth metrics (backend already provides these as percentages)
          sessionsGrowth: overviewData.tryOnsGrowth || 0,
          clientsGrowth: overviewData.clientsGrowth || 0,
          durationGrowth: overviewData.durationGrowth || 0,
          conversionGrowth: overviewData.conversionGrowth || 0,

          // Enhanced metrics (only show if we have data)
          engagementScore: enhancedData.avgEngagementScore || null,
          qualityScore: enhancedData.avgHandDetectionAccuracy && enhancedData.avgBackgroundRemovalQuality ?
            ((enhancedData.avgHandDetectionAccuracy + enhancedData.avgBackgroundRemovalQuality) / 2) * 100 : null,

          // Session trends from backend data
          sessionTrends: overviewData.trends?.map(trend => ({
            date: trend.date,
            sessions: trend.tryOns || 0
          })) || [],

          // Only show feature success rates if we have the data
          featureSuccessRates: enhancedData.avgHandDetectionAccuracy || enhancedData.avgBackgroundRemovalQuality ? [
            ...(enhancedData.avgHandDetectionAccuracy ? [{
              feature: 'Hand Detection',
              successRate: enhancedData.avgHandDetectionAccuracy * 100
            }] : []),
            ...(enhancedData.avgBackgroundRemovalQuality ? [{
              feature: 'Background Removal',
              successRate: enhancedData.avgBackgroundRemovalQuality * 100
            }] : []),
            ...(enhancedData.totalScreenshots && enhancedData.totalSessions ? [{
              feature: 'Screenshot Capture',
              successRate: (enhancedData.totalScreenshots / enhancedData.totalSessions) * 100
            }] : [])
          ] : null,

          // Recent activity based on actual data
          recentActivity: [
            {
              type: 'high_volume',
              title: 'Try-On Sessions',
              description: `${overviewData.totalSessions || 0} sessions in selected period`,
              timestamp: 'Current period'
            },
            {
              type: 'success_rate',
              title: 'Conversion Rate',
              description: `${(overviewData.conversionRate || 0).toFixed(1)}% conversion rate`,
              timestamp: 'Current period'
            },
            {
              type: 'new_client',
              title: 'Revenue Generated',
              description: `$${(overviewData.totalRevenue || 0).toLocaleString()} total revenue`,
              timestamp: 'Current period'
            }
          ]
        };

        setOverviewData(combinedData);
      } catch (err) {
        console.error('Error fetching overview data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOverviewData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading overview</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {overviewData?.totalSessions?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
              <Eye className="h-6 w-6 text-[#2D8C88]" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.sessionsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.sessionsGrowth >= 0 ? '+' : ''}{overviewData?.sessionsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Clients</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {overviewData?.activeClients?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Users className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.clientsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.clientsGrowth >= 0 ? '+' : ''}{overviewData?.clientsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Session Duration</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {(() => {
                  const duration = overviewData?.avgSessionDuration || 0;
                  if (duration >= 60) {
                    const minutes = Math.floor(duration / 60);
                    const seconds = Math.round(duration % 60);
                    return `${minutes}m ${seconds}s`;
                  }
                  return `${Math.round(duration)}s`;
                })()}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
              <Clock className="h-6 w-6 text-green-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.durationGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.durationGrowth >= 0 ? '+' : ''}{overviewData?.durationGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {(overviewData?.conversionRate || 0).toFixed(1)}%
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-purple-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.conversionGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.conversionGrowth >= 0 ? '+' : ''}{overviewData?.conversionGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>


      </div>

      {/* Enhanced Metrics Row - Only show if we have enhanced data */}
      {(overviewData?.engagementScore !== null || overviewData?.qualityScore !== null) && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {overviewData?.engagementScore !== null && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Engagement Score</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">
                    {Math.round(overviewData.engagementScore)}%
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-orange-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-600">
                  User interaction & engagement
                </span>
              </div>
            </motion.div>
          )}

          {overviewData?.qualityScore !== null && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Quality Score</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">
                    {Math.round(overviewData.qualityScore)}%
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-600">
                  Hand detection & background removal
                </span>
              </div>
            </motion.div>
          )}
        </div>
      )}

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Session Trends - Line Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends - Line Chart</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={overviewData?.sessionTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                  name="Total Sessions"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Session Trends - Area Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends - Area Chart</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={overviewData?.sessionTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  fill="#2D8C88"
                  fillOpacity={0.3}
                  name="Total Sessions"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Feature Success Rates - Only show if we have data */}
      {overviewData?.featureSuccessRates && overviewData.featureSuccessRates.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Feature Success Rates</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={overviewData.featureSuccessRates}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="feature" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="successRate" fill="#2D8C88" name="Success Rate" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      )}

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-white rounded-xl shadow-sm overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {overviewData?.recentActivity?.map((activity, index) => (
            <div key={index} className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  {activity.type === 'new_client' && <Users className="h-6 w-6 text-[#2D8C88]" />}
                  {activity.type === 'high_volume' && <Eye className="h-6 w-6 text-blue-500" />}
                  {activity.type === 'success_rate' && <TrendingUp className="h-6 w-6 text-green-500" />}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                  <p className="text-sm text-gray-500">{activity.description}</p>
                </div>
                <div className="ml-auto">
                  <p className="text-sm text-gray-500">{activity.timestamp}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default Overview;
